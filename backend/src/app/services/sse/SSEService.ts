import { Request, Response } from 'express';
import { Logger } from '../../config/logger';
import { redisOperations } from '../../config/redis';
import { auditLogService } from '../audit/AuditLogService';
import { retryService } from '../resilience/RetryService';

export interface SSEClient {
  id: string;
  userId: string;
  userType: 'student' | 'teacher' | 'admin';
  response: Response;
  rooms: Set<string>;
  lastHeartbeat: Date;
  connectionTime: Date;
  isActive: boolean;
}

export interface SSEMessage {
  id: string;
  type: string;
  data: any;
  timestamp: Date;
  targetUserId?: string;
  targetUserType?: 'student' | 'teacher' | 'admin';
  room?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  retryCount?: number;
  expiresAt?: Date;
}

export interface SSEConnectionStats {
  totalConnections: number;
  activeConnections: number;
  connectionsByUserType: Record<string, number>;
  averageConnectionTime: number;
  messagesSentLast24h: number;
  errorRate: number;
}

class SSEService {
  private clients: Map<string, SSEClient> = new Map();
  private userConnections: Map<string, Set<string>> = new Map(); // userId -> Set of clientIds
  private roomConnections: Map<string, Set<string>> = new Map(); // roomName -> Set of clientIds
  private messageQueue: Map<string, SSEMessage[]> = new Map(); // userId -> messages
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private connectionStats: SSEConnectionStats = {
    totalConnections: 0,
    activeConnections: 0,
    connectionsByUserType: {},
    averageConnectionTime: 0,
    messagesSentLast24h: 0,
    errorRate: 0
  };

  private readonly HEARTBEAT_INTERVAL = 30000; // 30 seconds
  private readonly CLIENT_TIMEOUT = 60000; // 60 seconds
  private readonly MAX_QUEUE_SIZE = 100;
  private readonly MAX_RETRY_COUNT = 3;

  constructor() {
    this.startHeartbeat();
    this.startCleanup();
    Logger.info('🔄 SSE Service initialized');
  }

  /**
   * Create new SSE connection
   */
  public createConnection(req: Request, res: Response, userId: string, userType: 'student' | 'teacher' | 'admin'): string {
    const clientId = this.generateClientId();
    
    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
      'X-Accel-Buffering': 'no', // Disable nginx buffering
    });

    // Create client object
    const client: SSEClient = {
      id: clientId,
      userId,
      userType,
      response: res,
      rooms: new Set(),
      lastHeartbeat: new Date(),
      connectionTime: new Date(),
      isActive: true
    };

    // Store client
    this.clients.set(clientId, client);
    
    // Update user connections mapping
    if (!this.userConnections.has(userId)) {
      this.userConnections.set(userId, new Set());
    }
    this.userConnections.get(userId)!.add(clientId);

    // Update stats
    this.connectionStats.totalConnections++;
    this.connectionStats.activeConnections++;
    this.connectionStats.connectionsByUserType[userType] = 
      (this.connectionStats.connectionsByUserType[userType] || 0) + 1;

    // Send welcome message
    this.sendToClient(clientId, {
      id: this.generateMessageId(),
      type: 'connection_established',
      data: {
        clientId,
        serverTime: new Date(),
        heartbeatInterval: this.HEARTBEAT_INTERVAL
      },
      timestamp: new Date(),
      priority: 'medium'
    });

    // Send queued messages
    this.sendQueuedMessages(userId);

    // Handle connection close
    req.on('close', () => {
      this.removeConnection(clientId);
    });

    // Log connection event
    auditLogService.logSSEConnection(userId, userType, 'connect', {
      clientId,
      connectionTime: client.connectionTime,
      userAgent: req.headers['user-agent'],
      ipAddress: req.ip
    });

    Logger.info(`📡 SSE connection established: ${clientId} for user ${userId} (${userType})`);
    return clientId;
  }

  /**
   * Send message to specific client
   */
  public sendToClient(clientId: string, message: SSEMessage): boolean {
    const client = this.clients.get(clientId);
    if (!client || !client.isActive) {
      return false;
    }

    try {
      const sseData = this.formatSSEMessage(message);
      client.response.write(sseData);
      client.lastHeartbeat = new Date();

      // Log successful message send
      auditLogService.log({
        service: 'sse',
        action: 'message_sent',
        userId: client.userId,
        userType: client.userType,
        details: {
          clientId,
          messageType: message.type,
          messageId: message.id,
          priority: message.priority
        },
        severity: 'info',
        source: 'backend',
        success: true
      });

      return true;
    } catch (error) {
      Logger.error(`❌ Failed to send SSE message to client ${clientId}:`, error);

      // Log error
      auditLogService.logSSEConnection(client.userId, client.userType, 'error', {
        clientId,
        error: (error as Error).message,
        messageType: message.type,
        messageId: message.id
      });

      this.removeConnection(clientId);
      return false;
    }
  }

  /**
   * Send message to specific user (all their connections)
   */
  public sendToUser(userId: string, message: SSEMessage): number {
    const clientIds = this.userConnections.get(userId);
    if (!clientIds || clientIds.size === 0) {
      // Queue message for when user connects
      this.queueMessage(userId, message);
      return 0;
    }

    let successCount = 0;
    for (const clientId of clientIds) {
      if (this.sendToClient(clientId, message)) {
        successCount++;
      }
    }

    return successCount;
  }

  /**
   * Send message to all users of a specific type
   */
  public sendToUserType(userType: 'student' | 'teacher' | 'admin', message: SSEMessage): number {
    let successCount = 0;
    
    for (const client of this.clients.values()) {
      if (client.userType === userType && client.isActive) {
        if (this.sendToClient(client.id, message)) {
          successCount++;
        }
      }
    }

    return successCount;
  }

  /**
   * Send message to specific room
   */
  public sendToRoom(roomName: string, message: SSEMessage): number {
    const clientIds = this.roomConnections.get(roomName);
    if (!clientIds || clientIds.size === 0) {
      return 0;
    }

    let successCount = 0;
    for (const clientId of clientIds) {
      if (this.sendToClient(clientId, { ...message, room: roomName })) {
        successCount++;
      }
    }

    return successCount;
  }

  /**
   * Broadcast message to all connected clients
   */
  public broadcast(message: SSEMessage): number {
    let successCount = 0;
    
    for (const client of this.clients.values()) {
      if (client.isActive) {
        if (this.sendToClient(client.id, message)) {
          successCount++;
        }
      }
    }

    return successCount;
  }

  /**
   * Join client to room
   */
  public joinRoom(clientId: string, roomName: string): boolean {
    const client = this.clients.get(clientId);
    if (!client) {
      return false;
    }

    client.rooms.add(roomName);
    
    if (!this.roomConnections.has(roomName)) {
      this.roomConnections.set(roomName, new Set());
    }
    this.roomConnections.get(roomName)!.add(clientId);

    Logger.info(`📚 Client ${clientId} joined room: ${roomName}`);
    return true;
  }

  /**
   * Remove client from room
   */
  public leaveRoom(clientId: string, roomName: string): boolean {
    const client = this.clients.get(clientId);
    if (!client) {
      return false;
    }

    client.rooms.delete(roomName);
    this.roomConnections.get(roomName)?.delete(clientId);

    Logger.info(`📚 Client ${clientId} left room: ${roomName}`);
    return true;
  }

  /**
   * Get connection statistics
   */
  public getConnectionStats(): SSEConnectionStats {
    return { ...this.connectionStats };
  }

  /**
   * Get connected users
   */
  public getConnectedUsers(): Array<{ userId: string; userType: string; connectionCount: number; rooms: string[] }> {
    const users: Map<string, { userType: string; connectionCount: number; rooms: Set<string> }> = new Map();

    for (const client of this.clients.values()) {
      if (client.isActive) {
        if (!users.has(client.userId)) {
          users.set(client.userId, {
            userType: client.userType,
            connectionCount: 0,
            rooms: new Set()
          });
        }
        
        const user = users.get(client.userId)!;
        user.connectionCount++;
        client.rooms.forEach(room => user.rooms.add(room));
      }
    }

    return Array.from(users.entries()).map(([userId, data]) => ({
      userId,
      userType: data.userType,
      connectionCount: data.connectionCount,
      rooms: Array.from(data.rooms)
    }));
  }

  /**
   * Private helper methods
   */
  private generateClientId(): string {
    return `sse_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private formatSSEMessage(message: SSEMessage): string {
    const data = JSON.stringify({
      id: message.id,
      type: message.type,
      data: message.data,
      timestamp: message.timestamp,
      priority: message.priority,
      room: message.room
    });

    return `id: ${message.id}\nevent: ${message.type}\ndata: ${data}\n\n`;
  }

  private queueMessage(userId: string, message: SSEMessage): void {
    if (!this.messageQueue.has(userId)) {
      this.messageQueue.set(userId, []);
    }

    const queue = this.messageQueue.get(userId)!;
    
    // Remove expired messages
    const now = new Date();
    const validMessages = queue.filter(msg => !msg.expiresAt || msg.expiresAt > now);
    
    // Add new message
    validMessages.push(message);
    
    // Limit queue size
    if (validMessages.length > this.MAX_QUEUE_SIZE) {
      validMessages.splice(0, validMessages.length - this.MAX_QUEUE_SIZE);
    }

    this.messageQueue.set(userId, validMessages);
  }

  private sendQueuedMessages(userId: string): void {
    const queue = this.messageQueue.get(userId);
    if (!queue || queue.length === 0) {
      return;
    }

    for (const message of queue) {
      this.sendToUser(userId, message);
    }

    this.messageQueue.delete(userId);
  }

  private removeConnection(clientId: string): void {
    const client = this.clients.get(clientId);
    if (!client) {
      return;
    }

    // Mark as inactive
    client.isActive = false;

    // Remove from user connections
    const userClients = this.userConnections.get(client.userId);
    if (userClients) {
      userClients.delete(clientId);
      if (userClients.size === 0) {
        this.userConnections.delete(client.userId);
      }
    }

    // Remove from rooms
    for (const roomName of client.rooms) {
      this.roomConnections.get(roomName)?.delete(clientId);
    }

    // Update stats
    this.connectionStats.activeConnections--;
    this.connectionStats.connectionsByUserType[client.userType] = 
      Math.max(0, (this.connectionStats.connectionsByUserType[client.userType] || 1) - 1);

    // Remove client
    this.clients.delete(clientId);

    // Log disconnection event
    auditLogService.logSSEConnection(client.userId, client.userType, 'disconnect', {
      clientId,
      connectionDuration: Date.now() - client.connectionTime.getTime(),
      reason: 'client_disconnect'
    });

    Logger.info(`📡 SSE connection closed: ${clientId} for user ${client.userId}`);
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      const heartbeatMessage: SSEMessage = {
        id: this.generateMessageId(),
        type: 'heartbeat',
        data: { timestamp: new Date() },
        timestamp: new Date(),
        priority: 'low'
      };

      for (const client of this.clients.values()) {
        if (client.isActive) {
          this.sendToClient(client.id, heartbeatMessage);
        }
      }
    }, this.HEARTBEAT_INTERVAL);
  }

  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      const now = new Date();
      const timeoutThreshold = new Date(now.getTime() - this.CLIENT_TIMEOUT);

      for (const [clientId, client] of this.clients.entries()) {
        if (client.lastHeartbeat < timeoutThreshold) {
          Logger.warn(`⚠️ Removing inactive SSE client: ${clientId}`);
          this.removeConnection(clientId);
        }
      }

      // Clean up expired queued messages
      for (const [userId, queue] of this.messageQueue.entries()) {
        const validMessages = queue.filter(msg => !msg.expiresAt || msg.expiresAt > now);
        if (validMessages.length === 0) {
          this.messageQueue.delete(userId);
        } else if (validMessages.length !== queue.length) {
          this.messageQueue.set(userId, validMessages);
        }
      }
    }, 60000); // Run cleanup every minute
  }

  /**
   * Cleanup on service shutdown
   */
  public shutdown(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // Close all connections
    for (const client of this.clients.values()) {
      try {
        client.response.end();
      } catch (error) {
        // Ignore errors during shutdown
      }
    }

    this.clients.clear();
    this.userConnections.clear();
    this.roomConnections.clear();
    this.messageQueue.clear();

    Logger.info('🔄 SSE Service shutdown complete');
  }
}

export default SSEService;
