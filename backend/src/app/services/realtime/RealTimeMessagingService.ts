import { Logger } from '../../config/logger';
import { sseService } from '../../routes/sseRoutes';
import { pollingService } from '../../routes/pollingRoutes';
import { redisOperations } from '../../config/redis';

export interface MessageUpdate {
  type: 'new_message' | 'message_read' | 'typing_start' | 'typing_stop' | 'conversation_update';
  data: any;
  conversationId: string;
  senderId?: string;
  recipientIds?: string[];
  timestamp: Date;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface TypingIndicator {
  userId: string;
  conversationId: string;
  isTyping: boolean;
  timestamp: Date;
}

export interface MessageDeliveryStatus {
  messageId: string;
  conversationId: string;
  deliveredTo: string[];
  readBy: string[];
  timestamp: Date;
}

class RealTimeMessagingService {
  private typingIndicators: Map<string, TypingIndicator[]> = new Map(); // conversationId -> typing users
  private messageQueue: Map<string, MessageUpdate[]> = new Map(); // userId -> queued messages
  private deliveryTracking: Map<string, MessageDeliveryStatus> = new Map(); // messageId -> status
  private cleanupInterval: NodeJS.Timeout | null = null;
  
  private readonly TYPING_TIMEOUT = 10000; // 10 seconds
  private readonly MESSAGE_EXPIRY = 7 * 24 * 60 * 60 * 1000; // 7 days
  private readonly CLEANUP_INTERVAL = 60000; // 1 minute

  constructor() {
    this.startCleanup();
    Logger.info('💬 Real-Time Messaging Service initialized');
  }

  /**
   * Broadcast new message to conversation participants
   */
  public broadcastNewMessage(conversationId: string, messageData: any): void {
    const update: MessageUpdate = {
      type: 'new_message',
      data: messageData,
      conversationId,
      senderId: messageData.senderId,
      timestamp: new Date(),
      priority: 'high'
    };

    // Send to conversation room via SSE
    this.sendToConversationRoom(conversationId, update);

    // Send to individual participants via polling
    if (messageData.recipientIds && Array.isArray(messageData.recipientIds)) {
      for (const recipientId of messageData.recipientIds) {
        this.sendPollingUpdate(recipientId, update);
      }
    }

    // Track delivery
    this.trackMessageDelivery(messageData.id, conversationId, messageData.recipientIds || []);

    Logger.info(`💬 New message broadcast to conversation ${conversationId}`);
  }

  /**
   * Broadcast message read status
   */
  public broadcastMessageRead(conversationId: string, readData: any): void {
    const update: MessageUpdate = {
      type: 'message_read',
      data: readData,
      conversationId,
      timestamp: new Date(),
      priority: 'medium'
    };

    // Send to conversation room
    this.sendToConversationRoom(conversationId, update);

    // Update delivery tracking
    if (readData.messageIds && Array.isArray(readData.messageIds)) {
      for (const messageId of readData.messageIds) {
        this.updateMessageReadStatus(messageId, readData.userId);
      }
    }

    Logger.debug(`💬 Message read status broadcast to conversation ${conversationId}`);
  }

  /**
   * Handle typing indicators
   */
  public handleTypingIndicator(conversationId: string, userId: string, isTyping: boolean): void {
    const indicator: TypingIndicator = {
      userId,
      conversationId,
      isTyping,
      timestamp: new Date()
    };

    // Update typing indicators
    if (!this.typingIndicators.has(conversationId)) {
      this.typingIndicators.set(conversationId, []);
    }

    const indicators = this.typingIndicators.get(conversationId)!;
    const existingIndex = indicators.findIndex(ind => ind.userId === userId);

    if (isTyping) {
      if (existingIndex >= 0) {
        indicators[existingIndex] = indicator;
      } else {
        indicators.push(indicator);
      }
    } else {
      if (existingIndex >= 0) {
        indicators.splice(existingIndex, 1);
      }
    }

    // Broadcast typing status
    const update: MessageUpdate = {
      type: isTyping ? 'typing_start' : 'typing_stop',
      data: {
        userId,
        isTyping,
        typingUsers: indicators.filter(ind => ind.isTyping).map(ind => ind.userId)
      },
      conversationId,
      timestamp: new Date(),
      priority: 'low'
    };

    this.sendToConversationRoom(conversationId, update);

    Logger.debug(`💬 Typing indicator updated: ${userId} ${isTyping ? 'started' : 'stopped'} typing in ${conversationId}`);
  }

  /**
   * Broadcast conversation update
   */
  public broadcastConversationUpdate(conversationId: string, updateData: any): void {
    const update: MessageUpdate = {
      type: 'conversation_update',
      data: updateData,
      conversationId,
      timestamp: new Date(),
      priority: 'medium'
    };

    this.sendToConversationRoom(conversationId, update);

    Logger.info(`💬 Conversation update broadcast to ${conversationId}`);
  }

  /**
   * Get typing indicators for conversation
   */
  public getTypingIndicators(conversationId: string): string[] {
    const indicators = this.typingIndicators.get(conversationId) || [];
    const now = new Date();
    
    // Filter out expired typing indicators
    const activeIndicators = indicators.filter(ind => 
      ind.isTyping && (now.getTime() - ind.timestamp.getTime()) < this.TYPING_TIMEOUT
    );

    return activeIndicators.map(ind => ind.userId);
  }

  /**
   * Get message delivery status
   */
  public getMessageDeliveryStatus(messageId: string): MessageDeliveryStatus | null {
    return this.deliveryTracking.get(messageId) || null;
  }

  /**
   * Get messaging statistics
   */
  public getMessagingStats(): {
    activeConversations: number;
    typingUsers: number;
    queuedMessages: number;
    trackedMessages: number;
  } {
    const activeConversations = this.typingIndicators.size;
    const typingUsers = Array.from(this.typingIndicators.values())
      .reduce((total, indicators) => total + indicators.filter(ind => ind.isTyping).length, 0);
    const queuedMessages = Array.from(this.messageQueue.values())
      .reduce((total, messages) => total + messages.length, 0);
    const trackedMessages = this.deliveryTracking.size;

    return {
      activeConversations,
      typingUsers,
      queuedMessages,
      trackedMessages
    };
  }

  /**
   * Private helper methods
   */
  private sendToConversationRoom(conversationId: string, update: MessageUpdate): void {
    try {
      const roomName = `conversation:${conversationId}`;
      
      // Send via SSE
      const sseMessage = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        type: update.type,
        data: update.data,
        timestamp: update.timestamp,
        room: roomName,
        priority: update.priority,
        expiresAt: new Date(Date.now() + this.MESSAGE_EXPIRY)
      };

      sseService.sendToRoom(roomName, sseMessage);

      // Send via Polling
      pollingService.broadcastToRoom(roomName, {
        type: update.type,
        data: update.data,
        priority: update.priority,
        expiresAt: new Date(Date.now() + this.MESSAGE_EXPIRY)
      });
    } catch (error) {
      Logger.error('❌ Failed to send message to conversation room:', error);
    }
  }

  private sendPollingUpdate(userId: string, update: MessageUpdate): void {
    try {
      const pollingUpdate = {
        id: `poll_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        type: update.type,
        data: update.data,
        timestamp: update.timestamp,
        userId,
        priority: update.priority,
        expiresAt: new Date(Date.now() + this.MESSAGE_EXPIRY)
      };

      pollingService.addUpdate(pollingUpdate);
    } catch (error) {
      Logger.error('❌ Failed to send polling message update:', error);
    }
  }

  private trackMessageDelivery(messageId: string, conversationId: string, recipientIds: string[]): void {
    const status: MessageDeliveryStatus = {
      messageId,
      conversationId,
      deliveredTo: [...recipientIds], // Assume delivered immediately for now
      readBy: [],
      timestamp: new Date()
    };

    this.deliveryTracking.set(messageId, status);

    // Store in Redis for persistence
    try {
      redisOperations.setex(
        `message_delivery:${messageId}`,
        7 * 24 * 60 * 60, // 7 days
        JSON.stringify(status)
      );
    } catch (error) {
      Logger.error('❌ Failed to store message delivery status in Redis:', error);
    }
  }

  private updateMessageReadStatus(messageId: string, userId: string): void {
    const status = this.deliveryTracking.get(messageId);
    if (status && !status.readBy.includes(userId)) {
      status.readBy.push(userId);
      status.timestamp = new Date();

      // Update in Redis
      try {
        redisOperations.setex(
          `message_delivery:${messageId}`,
          7 * 24 * 60 * 60, // 7 days
          JSON.stringify(status)
        );
      } catch (error) {
        Logger.error('❌ Failed to update message read status in Redis:', error);
      }
    }
  }

  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, this.CLEANUP_INTERVAL);
  }

  private performCleanup(): void {
    const now = new Date();

    // Clean up expired typing indicators
    for (const [conversationId, indicators] of this.typingIndicators.entries()) {
      const activeIndicators = indicators.filter(ind => 
        (now.getTime() - ind.timestamp.getTime()) < this.TYPING_TIMEOUT
      );

      if (activeIndicators.length === 0) {
        this.typingIndicators.delete(conversationId);
      } else if (activeIndicators.length !== indicators.length) {
        this.typingIndicators.set(conversationId, activeIndicators);
      }
    }

    // Clean up old message delivery tracking
    for (const [messageId, status] of this.deliveryTracking.entries()) {
      if ((now.getTime() - status.timestamp.getTime()) > this.MESSAGE_EXPIRY) {
        this.deliveryTracking.delete(messageId);
      }
    }

    // Clean up message queues
    for (const [userId, messages] of this.messageQueue.entries()) {
      const validMessages = messages.filter(msg => 
        (now.getTime() - msg.timestamp.getTime()) < this.MESSAGE_EXPIRY
      );

      if (validMessages.length === 0) {
        this.messageQueue.delete(userId);
      } else if (validMessages.length !== messages.length) {
        this.messageQueue.set(userId, validMessages);
      }
    }

    Logger.debug('💬 Real-time messaging cleanup completed');
  }

  /**
   * Shutdown service
   */
  public shutdown(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.typingIndicators.clear();
    this.messageQueue.clear();
    this.deliveryTracking.clear();

    Logger.info('💬 Real-Time Messaging Service shutdown complete');
  }
}

// Create singleton instance
const realTimeMessagingService = new RealTimeMessagingService();

export { realTimeMessagingService };
export default RealTimeMessagingService;
