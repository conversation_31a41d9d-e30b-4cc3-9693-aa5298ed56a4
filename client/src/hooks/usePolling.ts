import { useState, useEffect, useRef, useCallback } from 'react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

export interface PollingUpdate {
  id: string;
  type: string;
  data: any;
  timestamp: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface PollingBatch {
  id: string;
  updates: PollingUpdate[];
  timestamp: string;
  totalUpdates: number;
  hasMore: boolean;
}

export interface PollingStats {
  isActive: boolean;
  subscriptionId: string | null;
  lastPollTime: Date | null;
  nextPollTime: Date | null;
  updatesReceived: number;
  errorCount: number;
  currentInterval: number;
}

export interface UsePollingOptions {
  types?: string[];
  autoStart?: boolean;
  initialInterval?: number;
  maxInterval?: number;
  backoffMultiplier?: number;
  onUpdate?: (update: PollingUpdate) => void;
  onBatch?: (batch: PollingBatch) => void;
  onError?: (error: any) => void;
}

export const usePolling = (options: UsePollingOptions = {}) => {
  const {
    types = ['*'],
    autoStart = true,
    initialInterval = 5000,
    maxInterval = 30000,
    backoffMultiplier = 1.5,
    onUpdate,
    onBatch,
    onError
  } = options;

  const { user, token } = useAuth();
  const [stats, setStats] = useState<PollingStats>({
    isActive: false,
    subscriptionId: null,
    lastPollTime: null,
    nextPollTime: null,
    updatesReceived: 0,
    errorCount: 0,
    currentInterval: initialInterval
  });

  const pollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isUnmountingRef = useRef(false);
  const lastUpdateIdRef = useRef<string | null>(null);

  const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5000/api';

  /**
   * Subscribe to polling updates
   */
  const subscribe = useCallback(async (): Promise<string | null> => {
    if (!user || !token) {
      return null;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/polling/subscribe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ types })
      });

      if (!response.ok) {
        throw new Error(`Subscription failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      setStats(prev => ({
        ...prev,
        subscriptionId: data.subscriptionId,
        nextPollTime: new Date(data.nextPollTime)
      }));

      return data.subscriptionId;
    } catch (error) {
      console.error('Failed to subscribe to polling:', error);
      onError?.(error);
      return null;
    }
  }, [user, token, types, onError, API_BASE_URL]);

  /**
   * Unsubscribe from polling updates
   */
  const unsubscribe = useCallback(async () => {
    if (!stats.subscriptionId || !token) {
      return;
    }

    try {
      await fetch(`${API_BASE_URL}/polling/unsubscribe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ subscriptionId: stats.subscriptionId })
      });

      setStats(prev => ({
        ...prev,
        subscriptionId: null,
        isActive: false
      }));
    } catch (error) {
      console.error('Failed to unsubscribe from polling:', error);
    }
  }, [stats.subscriptionId, token, API_BASE_URL]);

  /**
   * Poll for updates
   */
  const poll = useCallback(async () => {
    if (!stats.subscriptionId || !token || isUnmountingRef.current) {
      return;
    }

    try {
      const url = new URL(`${API_BASE_URL}/polling/poll/${stats.subscriptionId}`);
      if (lastUpdateIdRef.current) {
        url.searchParams.append('lastUpdateId', lastUpdateIdRef.current);
      }

      const response = await fetch(url.toString(), {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Polling failed: ${response.statusText}`);
      }

      const result = await response.json();
      const batch: PollingBatch = result.data;

      setStats(prev => ({
        ...prev,
        lastPollTime: new Date(),
        nextPollTime: new Date(result.nextPollTime),
        updatesReceived: prev.updatesReceived + batch.totalUpdates,
        errorCount: 0,
        currentInterval: initialInterval // Reset interval on success
      }));

      // Process updates
      if (batch.updates.length > 0) {
        // Update last update ID
        lastUpdateIdRef.current = batch.updates[batch.updates.length - 1].id;

        // Call callbacks
        onBatch?.(batch);
        
        for (const update of batch.updates) {
          onUpdate?.(update);
          handlePollingUpdate(update);
        }
      }

      // Schedule next poll
      scheduleNextPoll(initialInterval);

    } catch (error) {
      console.error('Polling error:', error);
      
      setStats(prev => ({
        ...prev,
        errorCount: prev.errorCount + 1,
        currentInterval: Math.min(prev.currentInterval * backoffMultiplier, maxInterval)
      }));

      onError?.(error);

      // Schedule next poll with backoff
      scheduleNextPoll(stats.currentInterval);
    }
  }, [stats.subscriptionId, token, onUpdate, onBatch, onError, initialInterval, maxInterval, backoffMultiplier, API_BASE_URL]);

  /**
   * Schedule next polling attempt
   */
  const scheduleNextPoll = useCallback((interval: number) => {
    if (pollTimeoutRef.current) {
      clearTimeout(pollTimeoutRef.current);
    }

    if (isUnmountingRef.current || !stats.isActive) {
      return;
    }

    pollTimeoutRef.current = setTimeout(() => {
      if (!isUnmountingRef.current && stats.isActive) {
        poll();
      }
    }, interval);

    setStats(prev => ({
      ...prev,
      nextPollTime: new Date(Date.now() + interval)
    }));
  }, [poll, stats.isActive]);

  /**
   * Start polling
   */
  const start = useCallback(async () => {
    if (stats.isActive) {
      return;
    }

    const subscriptionId = await subscribe();
    if (subscriptionId) {
      setStats(prev => ({
        ...prev,
        isActive: true,
        subscriptionId
      }));

      // Start polling immediately
      setTimeout(() => {
        if (!isUnmountingRef.current) {
          poll();
        }
      }, 1000);
    }
  }, [stats.isActive, subscribe, poll]);

  /**
   * Stop polling
   */
  const stop = useCallback(async () => {
    if (pollTimeoutRef.current) {
      clearTimeout(pollTimeoutRef.current);
      pollTimeoutRef.current = null;
    }

    setStats(prev => ({
      ...prev,
      isActive: false
    }));

    await unsubscribe();
  }, [unsubscribe]);

  /**
   * Handle specific polling update types
   */
  const handlePollingUpdate = useCallback((update: PollingUpdate) => {
    switch (update.type) {
      case 'analytics_update':
      case 'analytics_batch':
        // Dispatch custom event for analytics components
        window.dispatchEvent(new CustomEvent('analytics-update', {
          detail: update.data
        }));
        break;
      case 'new_message':
        // Dispatch custom event for messaging components
        window.dispatchEvent(new CustomEvent('new-message', {
          detail: update.data
        }));
        break;
      case 'course_update':
        // Dispatch custom event for course components
        window.dispatchEvent(new CustomEvent('course-update', {
          detail: update.data
        }));
        break;
      case 'notification':
        handleNotification(update);
        break;
      default:
        console.debug('Unhandled polling update type:', update.type);
    }
  }, []);

  const handleNotification = (update: PollingUpdate) => {
    const { data } = update;
    
    const toastOptions = {
      duration: data.priority === 'urgent' ? 10000 : 5000,
      action: data.actionUrl ? {
        label: data.actionText || 'View',
        onClick: () => window.open(data.actionUrl, '_blank'),
      } : undefined,
    };

    switch (data.type) {
      case 'success':
        toast.success(data.title, { description: data.message, ...toastOptions });
        break;
      case 'error':
        toast.error(data.title, { description: data.message, ...toastOptions });
        break;
      case 'warning':
        toast.warning(data.title, { description: data.message, ...toastOptions });
        break;
      default:
        toast.info(data.title, { description: data.message, ...toastOptions });
    }
  };

  /**
   * Manual poll trigger
   */
  const triggerPoll = useCallback(() => {
    if (stats.isActive && stats.subscriptionId) {
      poll();
    }
  }, [stats.isActive, stats.subscriptionId, poll]);

  // Auto-start on mount
  useEffect(() => {
    if (autoStart && user && token) {
      start();
    }

    return () => {
      isUnmountingRef.current = true;
      stop();
    };
  }, [autoStart, user, token, start, stop]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isUnmountingRef.current = true;
      if (pollTimeoutRef.current) {
        clearTimeout(pollTimeoutRef.current);
      }
    };
  }, []);

  return {
    ...stats,
    start,
    stop,
    triggerPoll,
    subscribe,
    unsubscribe
  };
};
