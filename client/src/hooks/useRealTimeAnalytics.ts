import { useEffect, useRef, useState, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { io, Socket } from 'socket.io-client';
import { RootState } from '@/redux/store';
import { config } from '@/config';
import { toast } from '@/utils/toast';
import { Logger } from '@/utils/logger';

interface RealTimeUpdate {
  type: 'enrollment' | 'revenue' | 'activity' | 'performance' | 'pong' | 'lecture-update' | 'course-update' | 'analytics';
  data: any;
  timestamp: string;
}

interface UseRealTimeAnalyticsOptions {
  enableWebSocket?: boolean;
  pollingInterval?: number;
  onUpdate?: (update: RealTimeUpdate) => void;
}

export const useRealTimeAnalytics = (options: UseRealTimeAnalyticsOptions = {}) => {
  const {
    enableWebSocket = true,
    pollingInterval = 30000, // 30 seconds
    onUpdate
  } = options;

  const { user, token } = useSelector((state: RootState) => state.auth);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [connectionState, setConnectionState] = useState<'disconnected' | 'connecting' | 'connected' | 'reconnecting'>('disconnected');

  const socketRef = useRef<Socket | null>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const isUnmountingRef = useRef(false);
  const toastIdRef = useRef<string | number | null>(null);
  const lastToastTime = useRef<number>(0);

  // Enhanced cleanup function
  const cleanup = useCallback(() => {
    isUnmountingRef.current = true;

    // Dismiss any existing error toasts
    if (toastIdRef.current) {
      toast.dismiss(toastIdRef.current as string);
      toastIdRef.current = null;
    }

    // Cleanup Socket.io connection
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }

    // Cleanup polling
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }

    // Cleanup reconnect timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    setIsConnected(false);
    setConnectionError(null);
    setConnectionState('disconnected');
  }, []);

  // Enterprise Socket.io connection management with retry logic
  const connectSocket = useCallback(() => {
    if (!user?._id || !token || !enableWebSocket || isUnmountingRef.current) return;

    // Cleanup any existing connection
    if (socketRef.current) {
      socketRef.current.disconnect();
    }

    try {
      setConnectionState('connecting');

      const socket = io(config.wsBaseUrl, {
        auth: {
          token: token
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
        reconnection: false, // We'll handle reconnection manually for better control
      });

      socket.on('connect', () => {
        if (isUnmountingRef.current) {
          socket.disconnect();
          return;
        }

        Logger.info('Analytics Socket.io connected');
        setIsConnected(true);
        setConnectionError(null);
        setConnectionState('connected');
        reconnectAttempts.current = 0;

        // Dismiss any existing error toasts
        if (toastIdRef.current) {
          toast.dismiss(toastIdRef.current as string);
          toastIdRef.current = null;
        }

        // Join teacher-specific room for analytics updates
        if (user._id) {
          socket.emit('join_teacher_analytics', user._id);
        }
      });

      // Handle real-time analytics updates
      socket.on('analytics_update', (update: RealTimeUpdate) => {
        if (isUnmountingRef.current) return;

        try {
          Logger.info('Received real-time analytics update:', update.type);
          setLastUpdate(new Date());

          // Call the update handler if provided
          if (onUpdate) {
            onUpdate(update);
          }

          // Show immediate toast notification for important updates (with rate limiting)
          const now = Date.now();
          if (now - lastToastTime.current > 3000) { // Rate limit toasts to every 3 seconds
            if (update.type === 'enrollment') {
              toast.realTime.enrollment(update.data?.studentName);
              lastToastTime.current = now;
            } else if (update.type === 'revenue') {
              toast.realTime.payment(update.data?.amount || 0, update.data?.courseName);
              lastToastTime.current = now;
            }
          }
        } catch (error) {
          Logger.error('Failed to process analytics update:', error);
        }
      });

      // Handle connection errors with enterprise-level error handling
      socket.on('connect_error', (error) => {
        if (isUnmountingRef.current) return;

        Logger.error('Analytics Socket.io connection error:', error);
        setConnectionError('Connection failed');
        setConnectionState('disconnected');

        // Attempt exponential backoff reconnection
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
          reconnectAttempts.current++;
          setConnectionState('reconnecting');

          Logger.info(`Attempting to reconnect in ${delay}ms (attempt ${reconnectAttempts.current})`);

          reconnectTimeoutRef.current = setTimeout(() => {
            if (!isUnmountingRef.current) {
              connectSocket();
            }
          }, delay);
        } else {
          setConnectionError('Failed to reconnect after multiple attempts');
          if (!toastIdRef.current) {
            toastIdRef.current = toast.error('Real-time updates disconnected', {
              description: 'Please refresh the page to restore real-time updates.',
              duration: 10000
            });
          }
        }
      });

      socket.on('disconnect', (reason) => {
        if (isUnmountingRef.current) return;

        Logger.info('Analytics Socket.io disconnected:', reason);
        setIsConnected(false);
        setConnectionState('disconnected');

        // Attempt to reconnect if server initiated disconnect
        if (reason === 'io server disconnect' && reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
          reconnectAttempts.current++;
          setConnectionState('reconnecting');

          reconnectTimeoutRef.current = setTimeout(() => {
            if (!isUnmountingRef.current) {
              connectSocket();
            }
          }, delay);
        }
      });

      socketRef.current = socket;
    } catch (error) {
      Logger.error('Failed to create Socket.io connection:', error);
      setConnectionError('Failed to establish connection');
      setConnectionState('disconnected');
    }
  }, [user?._id, token, enableWebSocket, onUpdate]);

  // Polling fallback with improved cleanup
  const startPolling = useCallback(() => {
    if (!pollingInterval || pollingInterval <= 0 || isUnmountingRef.current) return;

    pollingRef.current = setInterval(() => {
      if (isUnmountingRef.current) return;

      // This would trigger a refetch of analytics data
      // The actual refetch is handled by the components using RTK Query
      setLastUpdate(new Date());

      if (onUpdate) {
        onUpdate({
          type: 'activity',
          data: { polling: true },
          timestamp: new Date().toISOString()
        });
      }
    }, pollingInterval);
  }, [pollingInterval, onUpdate]);

  // Initialize connections
  useEffect(() => {
    if (!user?._id) return;

    isUnmountingRef.current = false;

    if (enableWebSocket) {
      connectSocket();
    } else {
      startPolling();
    }

    return cleanup;
  }, [user?._id, enableWebSocket, connectSocket, startPolling, cleanup]);

  // Manual reconnect function
  const reconnect = useCallback(() => {
    if (isUnmountingRef.current) return;

    cleanup();

    reconnectAttempts.current = 0;
    setConnectionError(null);

    if (enableWebSocket) {
      connectSocket();
    } else {
      startPolling();
    }
  }, [enableWebSocket, connectSocket, startPolling, cleanup]);

  // Send message through Socket.io with safety checks
  const sendMessage = useCallback((message: any) => {
    if (isUnmountingRef.current) return false;

    if (socketRef.current && socketRef.current.connected) {
      try {
        socketRef.current.emit('analytics_message', message);
        return true;
      } catch (error) {
        Logger.error('Failed to send Socket.io message:', error);
        return false;
      }
    }
    return false;
  }, []);

  return {
    isConnected,
    connectionError,
    connectionState,
    lastUpdate,
    reconnect,
    sendMessage,
    isWebSocketEnabled: enableWebSocket,
    reconnectAttempts: reconnectAttempts.current,
    maxReconnectAttempts
  };
};
