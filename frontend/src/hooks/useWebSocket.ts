import { useState, useEffect, useRef, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
  delivered?: boolean;
  broadcast?: boolean;
  room?: string;
}

interface ConnectionStats {
  totalConnected: number;
  byUserType: Record<string, number>;
  activeUsers: string[];
}

interface UseWebSocketReturn {
  isConnected: boolean;
  isConnecting: boolean;
  lastMessage: WebSocketMessage | null;
  connectionStats: ConnectionStats | null;
  sendMessage: (event: string, data: any) => void;
  joinRoom: (roomName: string) => void;
  leaveRoom: (roomName: string) => void;
  reconnect: () => void;
  disconnect: () => void;
}

const WEBSOCKET_URL = process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'http://localhost:5000';

export const useWebSocket = (): UseWebSocketReturn => {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [connectionStats, setConnectionStats] = useState<ConnectionStats | null>(null);
  
  const socketRef = useRef<Socket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  
  const { user, token } = useAuth();

  // Initialize WebSocket connection
  const connect = useCallback(() => {
    if (!user || !token || socketRef.current?.connected) {
      return;
    }

    setIsConnecting(true);

    try {
      socketRef.current = io(WEBSOCKET_URL, {
        auth: {
          token: token,
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        forceNew: true,
      });

      const socket = socketRef.current;

      // Connection events
      socket.on('connect', () => {
        console.log('WebSocket connected:', socket.id);
        setIsConnected(true);
        setIsConnecting(false);
        reconnectAttempts.current = 0;
        
        toast.success('Connected to real-time updates', {
          duration: 2000,
        });
      });

      socket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason);
        setIsConnected(false);
        setIsConnecting(false);
        
        if (reason === 'io server disconnect') {
          // Server initiated disconnect, don't reconnect automatically
          toast.error('Disconnected from server');
        } else {
          // Client-side disconnect, attempt to reconnect
          scheduleReconnect();
        }
      });

      socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        setIsConnected(false);
        setIsConnecting(false);
        
        if (error.message.includes('Authentication')) {
          toast.error('Authentication failed. Please log in again.');
        } else {
          scheduleReconnect();
        }
      });

      // Welcome message
      socket.on('connected', (data) => {
        console.log('WebSocket welcome:', data);
        setConnectionStats(prev => ({
          ...prev,
          ...data.stats,
        }));
      });

      // Real-time notifications
      socket.on('notification', (notification) => {
        console.log('Real-time notification:', notification);
        
        setLastMessage({
          type: 'notification',
          data: notification,
          timestamp: notification.createdAt || new Date().toISOString(),
          delivered: true,
        });

        // Show toast notification based on priority
        const toastOptions = {
          duration: notification.priority === 'urgent' ? 10000 : 5000,
          action: notification.actionUrl ? {
            label: notification.actionText || 'View',
            onClick: () => window.open(notification.actionUrl, '_blank'),
          } : undefined,
        };

        switch (notification.priority) {
          case 'urgent':
            toast.error(notification.title, {
              description: notification.body,
              ...toastOptions,
            });
            break;
          case 'high':
            toast.warning(notification.title, {
              description: notification.body,
              ...toastOptions,
            });
            break;
          default:
            toast.info(notification.title, {
              description: notification.body,
              ...toastOptions,
            });
        }
      });

      // Status updates
      socket.on('status-update', (update) => {
        console.log('Status update:', update);
        
        setLastMessage({
          type: 'status-update',
          data: update,
          timestamp: update.updatedAt || new Date().toISOString(),
        });
      });

      // Stripe account updates
      socket.on('stripe-account-updated', (data) => {
        console.log('Stripe account updated:', data);
        
        setLastMessage({
          type: 'stripe-account-updated',
          data,
          timestamp: new Date().toISOString(),
        });
      });

      // Payout updates
      socket.on('payout-updated', (data) => {
        console.log('Payout updated:', data);
        
        setLastMessage({
          type: 'payout-updated',
          data,
          timestamp: new Date().toISOString(),
        });
      });

      // Broadcast notifications
      socket.on('broadcast-notification', (notification) => {
        console.log('Broadcast notification:', notification);
        
        setLastMessage({
          type: 'broadcast-notification',
          data: notification,
          timestamp: new Date().toISOString(),
          broadcast: true,
        });

        toast.info(notification.title, {
          description: notification.body,
          duration: 8000,
        });
      });

      // Room events
      socket.on('room-joined', (data) => {
        console.log('Joined room:', data.room);
        toast.success(`Joined ${data.room}`, { duration: 2000 });
      });

      socket.on('room-left', (data) => {
        console.log('Left room:', data.room);
      });

      // Connection stats updates
      socket.on('connection-stats', (stats) => {
        setConnectionStats(stats);
      });

    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      setIsConnecting(false);
      scheduleReconnect();
    }
  }, [user, token]);

  // Schedule reconnection with exponential backoff
  const scheduleReconnect = useCallback(() => {
    if (reconnectAttempts.current >= maxReconnectAttempts) {
      toast.error('Failed to reconnect. Please refresh the page.');
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
    reconnectAttempts.current++;

    console.log(`Scheduling reconnect attempt ${reconnectAttempts.current} in ${delay}ms`);

    reconnectTimeoutRef.current = setTimeout(() => {
      if (!socketRef.current?.connected) {
        toast.info(`Reconnecting... (attempt ${reconnectAttempts.current})`);
        connect();
      }
    }, delay);
  }, [connect]);

  // Manual reconnect
  const reconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (socketRef.current) {
      socketRef.current.disconnect();
    }
    
    reconnectAttempts.current = 0;
    connect();
  }, [connect]);

  // Disconnect
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    
    setIsConnected(false);
    setIsConnecting(false);
  }, []);

  // Send message
  const sendMessage = useCallback((event: string, data: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn('Cannot send message: WebSocket not connected');
    }
  }, []);

  // Join room
  const joinRoom = useCallback((roomName: string) => {
    sendMessage('join-room', roomName);
  }, [sendMessage]);

  // Leave room
  const leaveRoom = useCallback((roomName: string) => {
    sendMessage('leave-room', roomName);
  }, [sendMessage]);

  // Send activity heartbeat
  const sendActivity = useCallback(() => {
    sendMessage('activity', {});
  }, [sendMessage]);

  // Initialize connection when user is available
  useEffect(() => {
    if (user && token) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [user, token, connect, disconnect]);

  // Send activity heartbeat every 30 seconds
  useEffect(() => {
    if (isConnected) {
      const interval = setInterval(sendActivity, 30000);
      return () => clearInterval(interval);
    }
  }, [isConnected, sendActivity]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      disconnect();
    };
  }, [disconnect]);

  return {
    isConnected,
    isConnecting,
    lastMessage,
    connectionStats,
    sendMessage,
    joinRoom,
    leaveRoom,
    reconnect,
    disconnect,
  };
};
